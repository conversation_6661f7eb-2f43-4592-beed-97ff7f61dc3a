import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Alert, I18nManager, Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Row } from 'react-native-col';
import { Menu, MenuOption, MenuOptions, MenuTrigger, renderers } from 'react-native-popup-menu';
import { useIsConnected } from 'react-native-offline';
import { useDispatch } from 'react-redux';
import { SearchInput } from '../../common';
import { Close, Dots, Offline, RightArrows } from '../../../assets/svgs/icons';
import { colors, fonts } from '../../../utils/theme';
import { ErrorDialog, OrderClearModal, SaveDraftConfirmModal } from '../../modals';
import { HORIZONTAL_DIMENS, VERTICAL_DIMENS } from '../../../constants';
import { useAppSelector } from '../../../redux/hooks';

import {
	getBranchId,
	getCartProductsOffline,
	getCartShippingDetails,
	getCustomerExternalId,
	getCustomerLegalName,
	getCustomerName,
	getCustomerUserRoleId,
	getPrimaryContactName,
	getSalesPersonRoleIdRealm,
	getTenantCountry,
	getUserType
} from '../../../redux/selectors';
import { placeOrder, saveToDraft } from '../../../redux/apis/cart';
import { setCartSearchedProducts, setShippingNumberDetails, storeRemark } from '../../../redux/features/cart-slice';
import { ShippingNumber } from './ShippingNumber';
import { checkBadRequest } from '../../../utils/helpers';
import { RealmContext } from '../../../realm-database/RealmConfig';
import { getCustomerDetails } from '../../../redux/apis/customer';
import { clearTabletCartList, setCartItems, showPlaceOrderModal } from '../../../redux/features/cartOffline-slice';
import { sentryCaptureMessage, setSentryLog } from '../../../utils/sentry';
import { validateObject } from '../../../utils/validateObject';


const { useRealm } = RealmContext;

const CartTableActionsOffline = () => {
	const realm = useRealm();
	const { t } = useTranslation();
	const dispatch = useDispatch();
	const isConnected = useIsConnected();
	const userType = useAppSelector(getUserType);
	const customerName = useAppSelector(getCustomerName);
	const tenantCountry = useAppSelector(getTenantCountry);
	const customerLegalName = useAppSelector(getCustomerLegalName);
	const primaryContactName = useAppSelector(getPrimaryContactName);
	const customerExternalId = useAppSelector(getCustomerExternalId);
	const customerUserRoleId = useAppSelector(getCustomerUserRoleId);
	const salesPersonRoleId = useAppSelector(getSalesPersonRoleIdRealm);
	const shippingDetails: any = useAppSelector(getCartShippingDetails);
	const cartProducts = useAppSelector(getCartProductsOffline);
	const branchId = useAppSelector(getBranchId);
	const currentRole = useAppSelector(state => state.auth.currentRole);
	const savingToDraft = useAppSelector(state => state.cart.savingToDraft);
	const remarks = useAppSelector(state => state.cart.remark);

	const [showRemoveModal, setShowRemoveModal] = useState(false);
	const [loading, setLoading] = useState(false);
	const [showDraftAdd, setShowDraftAdd] = useState(false);
	const [searchKey, setSearchKey] = useState('');
	const [addressError, setAddressError] = useState(false);
	const [showShippingModal, setShowShippingModal] = useState(false);
	const [syncStarted, setsyncStarted] = useState(false);
	const [syncCompleted, setsyncCompleted] = useState(false);
	useEffect(() => {
		if (searchKey) {
			const searchedValue = searchKey.toLowerCase();
			const filteredProducts = cartProducts.filter((x) => {
				// console.log('match', x.value.toLowerCase(), x.value.toLowerCase().includes(searchedValue));
				return (
					x.product_name.toLowerCase().includes(searchedValue) || x.item_number.toLowerCase().includes(searchedValue)
				);
			});
			dispatch(setCartSearchedProducts({ searchKey, filteredProducts }));
		} else {
			dispatch(setCartSearchedProducts({ searchKey: '', filteredProducts: [] }));
		}
	}, [searchKey]);

	useEffect(() => {
		// console.log('🚀 ~ useEffect ~ isConnected confirm order:', isConnected);

		let isSyncingCompleted = false;
		let timeoutID: any;

		const checkSyncStatus = () => {
			if (isSyncingCompleted) {
				return;
			}

			// console.log('🚀 ~ useEffect ~ realm?.syncSession?.isConnected():', realm?.syncSession?.isConnected());

			setsyncStarted(true);
			setsyncCompleted(false);
			realm?.syncSession?.uploadAllLocalChanges()
				.then(() => realm?.syncSession?.downloadAllServerChanges())
				.then(() => {
					console.log('Local Realm DB is fully synced with the cloud');
					setsyncStarted(true);
					setsyncCompleted(true);  // Sync is successfully completed
					isSyncingCompleted = true; // Mark sync as completed locally

					// Clear the timeout if sync is completed
					if (timeoutID) {
						clearTimeout(timeoutID);
						// console.log('🚀 ~ Sync Completed ~ clearTimeout:');
					}
				})
				.catch(error => {
					console.error('Sync error:', error);
					// Retry sync after 5 seconds
					retrySync();
				});
		};

		const retrySync = () => {
			timeoutID = setTimeout(() => {
				if (!isSyncingCompleted && isConnected) {
					checkSyncStatus();
					console.log('🚀 ~ retrySync ~ checkSyncStatus:');
				}
			}, 5000);
		};

		if (isConnected) {
			checkSyncStatus();
		}

		// Cleanup function to clear the timeout if the component unmounts or dependencies change
		return () => {
			if (timeoutID) {
				clearTimeout(timeoutID);
				// console.log('🚀 ~ useEffect Cleanup ~ clearTimeout:');
			}
		};

	}, [isConnected]); // Depend on isConnected

	const PlaceOrder = async () => {
		// console.log("🚀 ~ PlaceOrder ~ PlaceOrder:");
		if (loading) {
			let requestBody: any = {};
			try {
				dispatch(showPlaceOrderModal(true));
				const customerResponse = await dispatch(getCustomerDetails({ userRoleId: customerUserRoleId })); // For getting sales person name

				if (!customerResponse.error) {
					const responsePayload = customerResponse.payload.data;
					const salePersonName = responsePayload.salesPersonDetail?.user_id?.first_name + ' ' + responsePayload.salesPersonDetail?.user_id?.last_name;
					requestBody = {
						tenantId: currentRole?.tenant_id?._id,
						salesPersonRoleId: salesPersonRoleId,
						customerUserRoleId: customerUserRoleId,
						customerName: customerName,
						salePersonName,
						branchId: branchId,
						orderPunchDeviceType: 'TABLET',
						orderPunchDeviceOs: Platform.OS.toUpperCase(),
						orderAppType: userType,
						ShippingAddress: shippingDetails.shippingAddress,
						cityId: shippingDetails.shippingCityId,
						regionId: shippingDetails.shippingRegionId,
						shippingMobileNumber: shippingDetails.shippingMobileNumber,
						shippingCountryCode: shippingDetails.shippingCountryCode,
						regionName: shippingDetails.region,
						cityName: shippingDetails.city,
						shippingCoordinates: {
							lat: shippingDetails.latitude,
							lng: shippingDetails.longitude
						},
						customerPrimaryContactName: primaryContactName,
						externalId: customerExternalId,
						customer_legal_name: customerLegalName,
						apiVersion: 2
					};
					if (remarks) { requestBody.orderRemark = remarks; }
					const NullishKeys = validateObject(requestBody,
						[
							'tenantId',
							'customerUserRoleId',
							'customerName',
							'customerPrimaryContactName',
							'salesPersonRoleId',
							'salePersonName',
							'branchId',
							'customer_legal_name',
							'orderPunchDeviceType',
							'orderPunchDeviceOs',
							'orderAppType',
							'ShippingAddress',
							'cityId',
							'regionId',
							'shippingMobileNumber',
							'shippingCountryCode',
							'regionName',
							'cityName',
							'shippingCoordinates.lat',
							'shippingCoordinates.lng']);
					// console.log("🚀 ~ PlaceOrder ~ NullishKeys:", NullishKeys)
					if (NullishKeys && Array.isArray(NullishKeys) && NullishKeys.length === 0) {
						const response = await dispatch(placeOrder(requestBody));
						// console.log('response of place order', JSON.stringify(response, null, 2));
						if (!response.error) {
							dispatch(storeRemark(null));
							dispatch(clearTabletCartList());
						} else {
							setSentryLog('placeOrderAPIError', '/productService/order', 'post', `${requestBody}`, JSON.stringify(response));

							Alert.alert(t(checkBadRequest(response.payload)));
						}
					} else {
						setSentryLog('placeOrderAPIError', '/productService/order', 'post', `${requestBody}`, `Invalid fields: ${NullishKeys.join(', ')}`);
						Alert.alert(`Missing fields values: ${NullishKeys.join(', ')}`);
					}

				} else {
					setSentryLog('GetCustomerAPIError', 'tenant-portal/customer', 'post', '', JSON.stringify(customerResponse?.payload?.message));
					Alert.alert(t(customerResponse?.payload?.message));
				}
			} catch (error) {
				console.log('In catch', `${error}`);
				setSentryLog('placeOrderCatchError', '/productService/order', 'post', `${requestBody}`, JSON.stringify(error));
				Alert.alert('Something went wrong!');
			} finally {
				setLoading(false);
			}
		}
	};

	useEffect(() => {
		PlaceOrder();
	}, [loading]);


	const toggleAddressError = () => {
		setAddressError(!addressError);
	};

	const handleRemoveModal = () => {
		setShowRemoveModal(!showRemoveModal);
	};

	const removeAllItemFromCart = async () => {
		//console.log('removeAllItemFromCart');
		const idsQuery = cartProducts.map(x => `oid(${x._id})`).join(', ');
		const cartItems = realm.objects('cart_items_20').filtered(`_id IN { ${idsQuery} }`);
		realm.write(() => {
			realm.delete(cartItems);
			handleRemoveModal();
			dispatch(setCartItems([]));
		});
	};

	const toggleSaveDraftModal = () => {
		if (!shippingDetails) {
			toggleAddressError();
			return;
		}
		setShowDraftAdd(!showDraftAdd);
	};

	const onSaveDraftConfirm = async () => {
		const requestBody = {
			customerName,
			salesPersonRoleId,
			customerUserRoleId,
			tenantId: currentRole?.tenant_id?._id,
			customerPrimaryContactName: customerName
		};
		const response = await dispatch(saveToDraft(requestBody));
		if (!response.error) {
			setShowDraftAdd(false);
		} else {
			Alert.alert(t(checkBadRequest(response.payload)));
		}
	};

	const onSetShippingNumber = (shippingMobileNumber: string) => {
		dispatch(setShippingNumberDetails({
			shippingCountryCode: tenantCountry.country_code,
			shippingMobileNumber
		}));
		setShowShippingModal(false);
	};


	const handlePlaceOrder = async () => {
		const shippingMobileNumber = shippingDetails.shippingMobileNumber;
		if (!+shippingMobileNumber) {
			setShowShippingModal(true); // Enter shipping mobile number first before place order
			return;
		}
		if (!loading) {
			setLoading(true);
		}
	};

	const onClearSearch = () => {
		setSearchKey('');
		dispatch(setCartSearchedProducts({ searchKey: '', filteredProducts: [] }));
	};

	return (
		<Row.LR style={styles.container}>
			<Text style={styles.title}>{t('cart')}</Text>
			<Row style={styles.rightItems}>
				<SearchInput
					value={searchKey}
					placeholder={t('navbar_search_placeholder')}
					inputContainerStyle={styles.searchInputContainer}
					inputStyle={styles.searchInput}
					numberOfLines={1}
					onChangeText={setSearchKey}
					onClear={onClearSearch}
				/>
				<TouchableOpacity
					style={[
						styles.confirmBtn,
						loading && styles.loading,
						(!shippingDetails || !isConnected || !syncCompleted) && styles.confirmBtnDisabled
					]}
					onPress={handlePlaceOrder}
					disabled={!shippingDetails || !isConnected || !syncCompleted || loading}
				>
					{
						loading
							? <ActivityIndicator color={colors.white} /> :

							syncStarted && !syncCompleted && isConnected ?
								<>
									<Text style={styles.confirmText}>{t('syncing')}...</Text>
									<ActivityIndicator size={'small'} color={colors.white} />
								</>
								:
								<>
									{
										isConnected === false ? <Offline style={{ marginRight: HORIZONTAL_DIMENS._10 }} fill={colors.white} /> : undefined
									}
									<Text style={styles.confirmText}>{t('confirm_order')}</Text>
									<RightArrows stroke={colors.white} style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }] }} />
								</>
					}
				</TouchableOpacity>
				<Menu
					renderer={renderers.Popover}
					rendererProps={{
						placement: 'auto',
						preferredPlacement: 'bottom',
						anchorStyle: styles.menuAnchorStyle
					}}
				>
					<MenuTrigger customStyles={triggerStyles}>
						<View style={styles.optionBtn}>
							<Dots fill={colors.primary} />
						</View>
					</MenuTrigger>
					<MenuOptions customStyles={optionsStyles}>
						{/* <MenuOption value={1} style={styles.menuOption} onSelect={toggleSaveDraftModal}>
							<DraftsInactive />
							<Text style={styles.menuOptionText}>{t('save_as_draft')}</Text>
						</MenuOption> */}
						<MenuOption value={2} style={styles.menuOption} onSelect={handleRemoveModal}>
							<Close width={20} height={20} fill={colors.primary} />
							<Text style={styles.menuOptionText}>{t('clear_order')}</Text>
						</MenuOption>
					</MenuOptions>
				</Menu>
			</Row>
			<OrderClearModal
				isVisible={showRemoveModal}
				onCancel={handleRemoveModal}
				onConfirm={removeAllItemFromCart}
				loading={false}
				title={t('cart_remove_all_confirm')}
			/>
			<SaveDraftConfirmModal
				isVisible={showDraftAdd}
				heading={t('confirm')}
				title={t('draft_save_confirm')}
				confirmButtonTitle={t('confirm')}
				onCancel={toggleSaveDraftModal}
				onConfirm={onSaveDraftConfirm}
				loading={savingToDraft}
			/>
			<ErrorDialog
				isVisible={addressError}
				title={t('save_draft_address_error')}
				onCancel={toggleAddressError}
			/>
			<ShippingNumber
				isVisible={showShippingModal}
				onConfirm={onSetShippingNumber}
				countryCode={tenantCountry.country_code}
				onBackdropPress={() => setShowShippingModal(false)}
			/>
		</Row.LR>
	);
};

const optionsStyles = {
	optionsContainer: {
		backgroundColor: colors.white,
		borderRadius: 10,
		paddingVertical: 12,
		width: 200
	},
	optionsWrapper: {
	},
	optionWrapper: {
		paddingVertical: 12,
		paddingHorizontal: 16
	},
	optionTouchable: {
		activeOpacity: 1
	}
};

const triggerStyles = {
	triggerOuterWrapper: {
		marginLeft: 10
	},
	TriggerTouchableComponent: TouchableOpacity
};

const styles = StyleSheet.create({
	container: {
		padding: VERTICAL_DIMENS._16,
		// marginHorizontal: 30,
		height: VERTICAL_DIMENS._88,
		paddingHorizontal: 30,
		backgroundColor: colors.grey100
	},
	title: {
		color: colors.darkGray,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._20,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	},
	orderNumber: {
		color: colors.secondary
	},
	rightItems: {
		alignItems: 'center'
	},
	searchInputContainer: {
		width: Platform.OS === 'ios' ? HORIZONTAL_DIMENS._448 : HORIZONTAL_DIMENS._448
	},
	searchInput: {
		paddingRight: HORIZONTAL_DIMENS._51
	},
	confirmBtn: {
		alignItems: 'center',
		backgroundColor: colors.secondary,
		borderRadius: 30,
		flexDirection: 'row',
		justifyContent: 'center',
		marginLeft: 10,
		paddingLeft: 36,
		paddingRight: 16,
		paddingVertical: 14
	},
	confirmBtnDisabled: {
		backgroundColor: colors.grey300
	},
	loading: {
		opacity: 0.6,
		paddingLeft: 80,
		//width: '20%',
		paddingRight: 80
	},
	confirmText: {
		color: colors.white,
		fontFamily: fonts.Montserrat.SemiBold,
		fontWeight: '600',
		fontSize: HORIZONTAL_DIMENS._14,
		textTransform: 'uppercase',
		marginRight: 18
	},
	optionBtn: {
		alignItems: 'center',
		backgroundColor: colors.white,
		borderRadius: 22,
		justifyContent: 'center',
		height: 44,
		width: 44,
		shadowColor: colors.black,
		shadowOffset: {
			width: 0,
			height: 0
		},
		shadowOpacity: 0.08,
		shadowRadius: 1,
		elevation: 1
	},
	menuAnchorStyle: {
		opacity: 0
	},
	menuTrigger: {
		marginLeft: 10
	},
	menuOption: {
		alignItems: 'center',
		flexDirection: 'row'
	},
	menuOptionText: {
		color: colors.darkGray,
		fontFamily: fonts.Montserrat.Medium,
		fontWeight: '500',
		fontSize: HORIZONTAL_DIMENS._16,
		paddingLeft: 12,
		writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr'
	}
});

export { CartTableActionsOffline };
