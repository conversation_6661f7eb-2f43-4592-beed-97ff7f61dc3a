/* eslint-disable @typescript-eslint/no-unused-vars */
import { Realm as ReactRealm } from '@realm/react';
import { isTablet } from 'react-native-device-info';
// import { UpdateMode } from 'realm';
// import Realm from 'realm';
import { UserRole } from './User';
import { MasterPrice } from './Master';

const connectionInital: any = {};
const appInitial: any = {};
const RealmDatabase = {
	connection: connectionInital,
	id: 'realm-device-db-mjfhq',
	app: appInitial,

	async getUser() {
		const credentials = ReactRealm.Credentials.anonymous();
		console.log('logged in');
		return await this.app.logIn(credentials);
	},

	async open(userId: string) {
		try {
			const config = {
				id: this.id
			};
			this.app = new ReactRealm.App(config);
			ReactRealm.App.Sync.setLogLevel(this.app, 'debug');
			console.log('realm app');
			if (!this.app?.currentUser) {
				await this.getUser();
			}
			console.log('got app');

			const openRealmBehaviorConfig: any = {
				type: 'openImmediately'
			};

			this.connection = await ReactRealm.open({
				schema: [UserRole, MasterPrice],
				sync: {
					user: this.app?.currentUser,
					flexible: true,
					initialSubscriptions: {
						update: (subs: any, realm: any) => {
							subs.add(
								realm.objects('master_price').filtered('tenant_id == 1119')
							);
						}
					},
					onError: (_session: any, error: any) => {
						// console.log('sync error');
						// console.log(error.name, error.message);
					},
					newRealmFileBehavior: openRealmBehaviorConfig,
					existingRealmFileBehavior: openRealmBehaviorConfig
				}
				// deleteRealmIfMigrationNeeded: true
			});
			console.log('connection made');

			const priceList = this.connection.objects('master_price').filtered('tenant_id == 1119');
			console.log('priceList in open', priceList);

			await this.connection.subscriptions.update((mutableSubscriptions: any) => {
				mutableSubscriptions.add(this.connection.objects('master_price').filtered('tenant_id == 1119'));
			});

			console.log('sub state', this.connection.subscriptions.state);

			const syncSession = this.connection.syncSession;
			const connectionState = syncSession.connectionState;
			console.log('connectionState', connectionState);
			syncSession.addProgressNotification(
				'upload',
				'reportIndefinitely',
				(transferred: any, transferable: any) => {
					console.log(`${transferred} bytes has been transferred`);
					console.log(
						`There are ${transferable} total transferable bytes, including the ones that have already been transferred`
					);
				}
			);

			syncSession.removeProgressNotification((transferred: any, transferable: any) => {
				console.log(`There was ${transferable} total transferable bytes`);
				console.log(`${transferred} bytes were transferred`);
			});
		} catch (error: any) {
			console.error('Failed to open the realm', error.message);
		}
	},

	close() {
		if (this.connection) {
			this.connection?.close();
		}
	},

	get getConnection() {
		return this.connection;
	},

	get getApp() {
		return ReactRealm.App.getApp(this.id);
	},

	getCustomers() {
		if (this.connection && isTablet()) {
			const customers = this.connection.objects('Customer');
			return customers;
		} else {
			return [];
		}
	},

	async getPriceList() {
		console.log('getPriceList called', this.connection);
		if (this.connection) {
			try {
				const priceList = this.connection?.objects('master_price').filtered('tenant_id == 1119');
				console.log('priceList in offline open', priceList);
			} catch (error) {
				console.log('getPriceList err', error);
			}
		}
	}
};

export default RealmDatabase;