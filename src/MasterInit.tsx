import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { AppProvider } from '@realm/react';
import Config from 'react-native-config';
import Toast from 'react-native-toast-message';
import dynamicLinks from '@react-native-firebase/dynamic-links';
import { useNavigation } from '@react-navigation/native';
import { isTablet } from 'react-native-device-info';
// import ConnectivityContainer from './ConnectivityContainer';
import FlashMessageConfig from './components/common/FlashMessageConfig';
import { CodePushUpdateModal, ForceUpdateModal, NoInternetModal } from './components/modals';
import { useAppSelector } from './redux/hooks';
import toastConfig from './utils/toastConfig';
import { getAllSalesPerson, getCustomerDetails, getPriceList } from './redux/apis/customer';
import { getTenantCities, getBrandsList, getTagList, getTaxSettings, getTaxTypes } from './redux/apis/common';
import { getUnitList } from './redux/apis/product';
import { checkOrder } from './redux/apis/orders';
import { getCurrentShiftActivity, salesPersonWorkShift } from './redux/apis/tracking';
import { getCustomerUserRoleId, getUserType } from './redux/selectors';
import { USER_TYPES } from './constants';
import { getUserSetting } from './redux/apis/sttings';
import ConnectivityContainer from './ConnectivityContainer';
import useNotifications from './hooks/useNotifications';
import { getNotificationUnReadCount } from './redux/apis/notification';

export const MasterInit: React.FC<{
	children: React.ReactNode;
}> = ({ children }) => {
	const dispatch = useDispatch();
	const navigation = useNavigation<any>();
	const userType = useAppSelector(getUserType);
	const { isLoggedIn } = useAppSelector((state) => state.auth);
	const currentRole = useAppSelector(state => state.auth.currentRole);
	const customerUserRoleId = useAppSelector(getCustomerUserRoleId);


	// Initialize Notifications - only for mobile devices, not tablets
	const { listenForNotifications, createNotificationChannel, requestPermissions, syncBadgeCount } = useNotifications();

	useEffect(() => {
		const unsubscribe = dynamicLinks().onLink(handleDynamicLink); // Foreground deep link event

		dynamicLinks()
			.getInitialLink() // Background deep link event
			.then((link: any) => {
				// console.log('background link', link);
				if (link) {
					handleOrderDeepLink(link);
				}
			});

		return () => unsubscribe();
	}, []);

	useEffect(() => {
		/* Check if user is logged in */
		if (isLoggedIn) {
			/* Loads app data on app launch or when current role is switched */
			loadData();
		}
	}, [isLoggedIn]);

	const handleDynamicLink = (link: any) => {
		if (link) {
			handleOrderDeepLink(link);
		}
	};

	const checkOrderForUser = async (orderId: string) => {
		if (orderId === 'dev.hawak.io') {
			if (isTablet()) {
				navigation.navigate('Orders');
			} else {
				navigation.navigate('Home');
			}
			return;
		}

		const requestBody = {
			orderId,
			tenantId: currentRole?.tenant_id?._id,
			userRoleId: currentRole?._id,
			portalType: userType,
			apiVersion: 2
		};
		const response = await dispatch(checkOrder(requestBody));
		// If success then open order details otherwise navigate to orders listing
		if (!response.error) {
			if (isTablet()) {
				navigation.navigate('OrderDetails', { orderId });
			} else {
				navigation.navigate('TrackOrder', { orderId });
			}
		} else {
			navigation.navigate('Orders');
		}
	};

	const handleOrderDeepLink = (link: any) => {
		if (link?.url && link?.url?.includes('apps.apple.com')) {
			const regex = /[?&]([^=#]+)=([^&#]*)/g;
			let params: any = {};
			let match;
			// eslint-disable-next-line no-cond-assign
			while (match = regex.exec(link.url)) {
				params[match[1]] = match[2];
			}
			const { orderid } = params;
			if (orderid) {
				checkOrderForUser(orderid);
			}
			return;
		}

		const parts = link?.url?.split('/');
		const orderId = parts.pop() || parts.pop();  // handle potential trailing slash
		checkOrderForUser(orderId);
	};

	const loadSettings = async () => {
		await dispatch(getUserSetting());
		await dispatch(getPriceList());
	};



	const getSalesPersonTrackingData = async () => {
		if (userType === USER_TYPES.SALES_APP && isTablet() === false) {
			await dispatch(getCurrentShiftActivity()); // Current ongoing shift and current ongoing activity in shift
			await dispatch(salesPersonWorkShift()); // Get shift hours configuration
			// LocationTracking.init(currentRole?.tenant_id?._id, currentRole._id);
		}
	};

	const getCustomerData = async () => {
		if (userType === USER_TYPES.CUSTOMER_APP) {
			await dispatch(getCustomerDetails({ userRoleId: customerUserRoleId }));
		}
	};

	const loadData = () => {
		if (isTablet()) {
			dispatch(getBrandsList());
			dispatch(getTagList());
			// dispatch(getUserSetting());
			currentRole.portal_type !== USER_TYPES.SALES_APP && dispatch(getAllSalesPerson());
		} else {
			// Mobile-only initialization
			loadSettings();
			dispatch(getTenantCities());
			dispatch(getUnitList());
			dispatch(getBrandsList());
			dispatch(getTagList());
			dispatch(getTaxSettings());
			dispatch(getTaxTypes());
			currentRole.portal_type !== USER_TYPES.SALES_APP && dispatch(getAllSalesPerson());
			getSalesPersonTrackingData();
			// Notification initialization (useNotifications hook has device checks)
			requestPermissions();
			createNotificationChannel();
			listenForNotifications();
			dispatch(getNotificationUnReadCount());
			// Sync badge count after getting notification count
			setTimeout(() => {
				syncBadgeCount();
			}, 1000);
			getCustomerData();
		}
	};

	return (
		<>
			<AppProvider id={Config.REALM_APP_ID}>
				<ForceUpdateModal />
				<CodePushUpdateModal />
				<FlashMessageConfig />
				<ConnectivityContainer />
				<NoInternetModal />
				{children}
				<Toast
					position="bottom"
					config={toastConfig}
					bottomOffset={0}
				/>
			</AppProvider>
		</>
	);
};

export default MasterInit;