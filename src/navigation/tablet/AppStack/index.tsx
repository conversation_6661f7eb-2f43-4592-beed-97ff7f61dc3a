import React, { memo } from 'react';
import AppSync from './AppSync';
import { UserProvider } from '@realm/react';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { RealmContext } from '../../../realm-database/RealmConfig';
import RealmAuthCheck from './RealmAuthCheck';

const openRealmBehaviorConfig: any = {
	type: 'openImmediately'
};

export default memo(() => {
	const { RealmProvider } = RealmContext;
	// const currentRole = useAppSelector(state => state.auth.currentRole);
	// const tenantId = currentRole?.tenant_id?._id;
	// console.log('tenantId', tenantId);
	// const countryId = currentRole?.tenant_id?.country?._id;

	const handleSyncError = async (session: any, syncError: any) => {
		if (syncError.name === 'ClientReset') {
			console.log('syncError ClientReset', syncError);
			try {
				console.log('error type is ClientReset....');
			} catch (err) {
				console.error(err);
			}
		} else {
			console.log('other sync error', syncError);
			// ...handle other error types
		}
	};

	return (
		<UserProvider fallback={RealmAuthCheck}>
			<RealmProvider
				sync={{
					flexible: true,
					onError: handleSyncError,
					newRealmFileBehavior: openRealmBehaviorConfig,
					existingRealmFileBehavior: openRealmBehaviorConfig
					// initialSubscriptions: {
					// 	update(subs, realm) {
					// 		subs.add(realm.objects('master_tax').filtered(`tenant_id == ${tenantId}`));
					// 		subs.add(realm.objects('tax_configuration').filtered(`tenant_id == ${tenantId}`));
					// 		subs.add(realm.objects('city').filtered(`is_deleted == false && country_id == oid(${countryId})`));
					// 		subs.add(realm.objects('region').filtered('is_deleted == false'));
					// 		subs.add(realm.objects('tenant_app_setting').filtered(`tenant_id == ${tenantId}`)); // Tenant app settings
					// 		subs.add(realm.objects('master_price').filtered(`tenant_id == ${tenantId}`)); // Fetch master price list to show default customer type
					// 		subs.add(realm.objects('master_unit').filtered(`tenant_id == ${tenantId}`));
					// 		subs.add(realm.objects('master_attribute').filtered(`tenant_id == ${tenantId}`));
					// 	}
					// }
					// clientReset: {
					// 	mode: Realm.ClientResetMode.DiscardUnsyncedChanges,
					// 	onBefore: realm => {
					// 		// NOT used with destructive schema changes
					// 		console.log('Beginning client reset for ', realm.path);
					// 	},
					// 	onAfter: (beforeRealm, afterRealm) => {
					// 		// Destructive schema changes do not hit this function.
					// 		// Instead, they go through the error handler.
					// 		console.log('Finished client reset for', beforeRealm.path);
					// 		console.log('New realm path', afterRealm.path);
					// 	}
					// }
				}}
			>
				<BottomSheetModalProvider>
					<AppSync />
				</BottomSheetModalProvider>
			</RealmProvider>
		</UserProvider>
	);
});