/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import Config from 'react-native-config';
import { useApp } from '@realm/react';
import AppStack from './AppStack';
import { RealmContext } from '../../../realm-database/RealmConfig';
import { useAppSelector } from '../../../redux/hooks';
import { setLoadAppStack, setLoadingData, setSyncedOnce } from '../../../redux/features/auth-slice';
import SynchronizationLoader from '../../../components/loader/SynchronizationLoader';
import { ReSyncLoader } from '../../../components/tablet/sync-offline-loader/ReSyncLoader';
import { UpdatingProductsLoader } from '../../../components/tablet/sync-offline-loader/UpdatingProductLoader';
import { CartSyncModal } from '../../../components/modals';
import { subMonths } from 'date-fns';
import { ProgressNotificationCallback } from 'realm';

const { useRealm } = RealmContext;

enum ProgressDirection {
	Download = 'download',
	Upload = 'upload',
}

enum ProgressMode {
	ReportIndefinitely = 'reportIndefinitely',
	ForCurrentlyOutstandingWork = 'forCurrentlyOutstandingWork',
}

const AppSync = () => {
	const app = useApp();
	const realm = useRealm();
	const dispatch = useDispatch();
	let currentTime = useMemo(() => new Date().getTime(), []);
	const last3Months = subMonths(new Date(), 3);
	// const result = useQuery('user_role');
	const currentRole = useAppSelector((state) => state.auth.currentRole);
	const syncedOnce = useAppSelector((state) => state.auth.syncedOnce);
	const loadAppstack = useAppSelector((state) => state.auth.loadAppstack);
	const tenantId = currentRole?.tenant_id?._id;
	const countryId = currentRole?.tenant_id?.country?._id;
	// const priceList = useMemo(() => result.sorted('created_at'), [result]);
	// const customers = useMemo(() => result.filtered(`tenant_id == ${tenantId}`), [result]);
	// console.log('customers', JSON.stringify(customers));

	useEffect(() => {
		let timer: any;
		if (syncedOnce) {
			timer = setTimeout(() => {
				dispatch(setLoadAppStack(true));
			}, 10);
		}
		return () => {
			if (timer) {clearTimeout(timer);}
		};

	}, [syncedOnce]);

	useEffect(() => {
		if (__DEV__) {
			Realm.App.Sync.setLogLevel(app, 'error');
		}

		const progressNotificationCallback: ProgressNotificationCallback = (transferred, transferable) => {
			// Convert decimal to percent with no decimals
			// (e.g. 0.6666... -> 67)
			const percentTransferred = parseFloat((transferred / transferable).toFixed(2)) * 100;
			console.log('percentTransferred', { transferred }, { transferable }, { percentTransferred });
		};

		// Listen for changes to connection state
		realm.syncSession?.addProgressNotification(
			ProgressDirection.Download,
			ProgressMode.ForCurrentlyOutstandingWork,
			progressNotificationCallback
		);
		console.log('Progress Notification: ' + (new Date().getTime() - currentTime) + ' milliseconds');
		realm.syncSession?.downloadAllServerChanges().then(() => {
			console.log('Sync Session: ' + (new Date().getTime() - currentTime) + ' milliseconds');
			console.log('downloaded');
		}).catch((error: any) => {
			console.log('somthing wrong', error);
		});
		// Remove the connection listener when component unmounts
		return () => {
			realm.syncSession?.removeProgressNotification(progressNotificationCallback);
			if (!realm.isClosed) {realm.close();}
		};
	}, []);
	useEffect(() => {
		updateSubsription();
	}, [tenantId]);


	/* -------------------------------------------------------------------------- */
	/*        subscription to realmDB collection to sync with local realmDB       */
	/* -------------------------------------------------------------------------- */
	const updateSubsription = async () => {
		try {
			console.log('start time: ' + (new Date().getTime() - currentTime) + ' milliseconds');
			dispatch(setLoadingData(true));
			await realm.subscriptions.update(mutableSubs => {
				mutableSubs.add(realm.objects('master_tax').filtered(`tenant_id == ${tenantId}`));
				mutableSubs.add(realm.objects('tax_configuration').filtered(`tenant_id == ${tenantId}`));
				mutableSubs.add(realm.objects('city').filtered(`is_deleted == false && country_id == oid(${countryId})`));
				mutableSubs.add(realm.objects('region').filtered('is_deleted == false'));
				mutableSubs.add(realm.objects('tenant_app_setting').filtered(`tenant_id == ${tenantId}`)); // Tenant app settings
				mutableSubs.add(realm.objects('user_role_setting').filtered(`tenant_id == ${tenantId} && _id == '${tenantId}_${currentRole._id}'`)); // Tenant app settings
				mutableSubs.add(realm.objects('master_price').filtered(`tenant_id == ${tenantId}`)); // Fetch master price list to show default customer type
				mutableSubs.add(realm.objects('master_unit').filtered(`tenant_id == ${tenantId}`));
				mutableSubs.add(realm.objects('tenant_customer')); // Tenant customer for user_role relationship
				mutableSubs.add(realm.objects('master_attribute').filtered(`tenant_id == ${tenantId}`));
				// Only customers and sales persons from user_role
				const onlyCustomers = realm.objects('user_role')
					.filtered(`tenant_id == ${tenantId} && role_id IN { oid(${Config.USER_ROLE_ID}), oid(${Config.SALES_USER_ROLE_ID}) } || _id == oid(${currentRole._id})`);
				mutableSubs.add(onlyCustomers);
				mutableSubs.add(realm.objects('products_20').filtered(`tenant_id == ${tenantId} && is_deleted == false && is_active == true`));
				mutableSubs.add(realm.objects('variant_types_20').filtered(`tenant_id == ${tenantId}`));
				mutableSubs.add(realm.objects('category').filtered(`tenant_id == ${tenantId} && is_deleted == false`));
				mutableSubs.add(realm.objects('images_20').filtered(`tenant_id == ${tenantId}`));
				mutableSubs.add(realm.objects('deal').filtered(`tenant_id == ${tenantId} && deal_status IN { 'RUNNING', 'ENDED', 'SCHEDULED' }`));
				mutableSubs.add(realm.objects('deal_product').filtered(`tenant_id == ${tenantId} && is_active == true`));
				mutableSubs.add(realm.objects('favorite_products_20').filtered(`tenant_id == ${tenantId} && user_role_id == oid(${currentRole._id})`));
				mutableSubs.add(realm.objects('cart_items_20').filtered(`tenant_id == ${tenantId} && cart_id ENDSWITH $0`, `_${currentRole._id}`));
				mutableSubs.add(realm.objects('order').filtered(`tenant_id == ${tenantId} && order_creator_user_role_id == oid(${currentRole._id}) && created_at >= $0`, last3Months)); // order_creator_user_role_id
				// mutableSubs.add(realm.objects('order_item').filtered(`tenant_id == ${tenantId} && order_creator_user_role_id == oid(${currentRole._id})`));
				// mutableSubs.add(realm.objects('drafts_20').filtered(`tenant_id == ${tenantId} && draft_owner_user_role_id == oid(${currentRole._id})`)); // draft_owner_user_role_id
				// mutableSubs.add(realm.objects('draft_items_20').filtered(`tenant_id == ${tenantId}`));
			});
			console.log('subscription End time: ' + (new Date().getTime() - currentTime) + ' milliseconds');
			const syncSession = realm.syncSession;
			const connectionState = syncSession?.connectionState;
			console.log('syncSession End time: ' + (new Date().getTime() - currentTime) + ' milliseconds');
			console.log('connectionState', connectionState);
			if (connectionState === 'connected') {
				dispatch(setSyncedOnce());
			}
		} catch (error) {
			console.log('Error adding query subscription: ', error);
		}
	};

	return (syncedOnce
		?
		<>
			<ReSyncLoader />
			<UpdatingProductsLoader/>
			{loadAppstack && <AppStack />}
			<CartSyncModal />
		</>
		:
		<SynchronizationLoader />);
};

export default AppSync;
